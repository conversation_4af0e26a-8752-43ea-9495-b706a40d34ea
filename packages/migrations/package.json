{"name": "migrations", "version": "1.0.0", "description": "Package to handle Db migrations for all microservices DBs", "main": "lib/migrations.js", "scripts": {"db:migrate": "run-s db:migrate:*", "db:migrate-down": "run-s db:migrate-down:*", "db:migrate-reset": "run-s db:migrate-reset:*", "db:migrate:authentication-service": "db-migrate up --config 'authentication-service/database.json' -m 'authentication-service/migrations'", "db:migrate-down:authentication-service": "db-migrate down --config 'authentication-service/database.json' -m 'authentication-service/migrations'", "db:migrate-reset:authentication-service": "db-migrate reset --config 'authentication-service/database.json' -m 'authentication-service/migrations'", "db:migrate:user-tenant-service": "db-migrate up --config 'user-tenant-service/database.json' -m 'user-tenant-service/migrations'", "db:migrate-down:user-tenant-service": "db-migrate down --config 'user-tenant-service/database.json' -m 'user-tenant-service/migrations'", "db:migrate-reset:user-tenant-service": "db-migrate reset --config 'user-tenant-service/database.json' -m 'user-tenant-service/migrations'", "db:migrate:audit-service": "db-migrate up --config 'audit-service/database.json' -m 'audit-service/migrations'", "db:migrate-down:audit-service": "db-migrate down --config 'audit-service/database.json' -m 'audit-service/migrations'", "db:migrate-reset:audit-service": "db-migrate reset --config 'audit-service/database.json' -m 'audit-service/migrations'", "db:migrate:notification-service": "db-migrate up --config 'notification-service/database.json' -m 'notification-service/migrations'", "db:migrate-down:notification-service": "db-migrate down --config 'notification-service/database.json' -m 'notification-service/migrations'", "db:migrate-reset:notification-service": "db-migrate reset --config 'notification-service/database.json' -m 'notification-service/migrations'"}, "author": "", "license": "ISC", "dependencies": {"db-migrate": "^1.0.0-beta.21", "dotenv": "^16.4.5", "dotenv-extended": "^2.9.0", "kindof": "^2.0.0", "db-migrate-pg": "^1.3.0"}, "devDependencies": {"@types/dotenv": "^8.2.0", "npm-run-all": "^4.1.5"}}