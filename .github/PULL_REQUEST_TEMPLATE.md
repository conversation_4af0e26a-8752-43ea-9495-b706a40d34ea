## Description

Please include a summary of the change and which issue is fixed. Please also include relevant motivation and context. List any dependencies that are required for this change.

Fixes # (issue)

## Type of change

Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Intermediate change (work in progress)

## How Has This Been Tested?

Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Please also list any relevant details for your test configuration

- [ ] Test A
- [ ] Test B

## Checklist:

- [ ] Performed a self-review of my own code
- [ ] npm test passes on your machine
- [ ] New tests added or existing tests modified to cover all changes
- [ ] Code conforms with the style guide
- [ ] API Documentation in code was updated
- [ ] Any dependent changes have been merged and published in downstream modules
