<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets/app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:2,&quot;tests&quot;:3,&quot;passes&quot;:3,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-07-04T04:26:28.091Z&quot;,&quot;end&quot;:&quot;2025-07-04T04:26:28.194Z&quot;,&quot;duration&quot;:103,&quot;testsRegistered&quot;:3,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;1e27e35c-a37c-4100-9db2-21fc13827a8e&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;d0accec7-f69f-4b60-bf7f-cbb153342fa9&quot;,&quot;title&quot;:&quot;HomePage&quot;,&quot;fullFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/dist/__tests__/acceptance/home-page.acceptance.js&quot;,&quot;file&quot;:&quot;/dist/__tests__/acceptance/home-page.acceptance.js&quot;,&quot;beforeHooks&quot;:[{&quot;title&quot;:&quot;\&quot;before all\&quot; hook: setupApplication in \&quot;HomePage\&quot;&quot;,&quot;fullTitle&quot;:&quot;HomePage \&quot;before all\&quot; hook: setupApplication in \&quot;HomePage\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:70,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;({ app, client } = await (0, test_helper_1.setupApplication)());&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;a9a4afba-fe8f-476b-a659-011ca8342870&quot;,&quot;parentUUID&quot;:&quot;d0accec7-f69f-4b60-bf7f-cbb153342fa9&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;afterHooks&quot;:[{&quot;title&quot;:&quot;\&quot;after all\&quot; hook in \&quot;HomePage\&quot;&quot;,&quot;fullTitle&quot;:&quot;HomePage \&quot;after all\&quot; hook in \&quot;HomePage\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:0,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await app.stop();&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0ae0b2a7-7776-40a9-ac3d-419750acafa8&quot;,&quot;parentUUID&quot;:&quot;d0accec7-f69f-4b60-bf7f-cbb153342fa9&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;tests&quot;:[{&quot;title&quot;:&quot;exposes a default home page&quot;,&quot;fullTitle&quot;:&quot;HomePage exposes a default home page&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:14,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await client\n    .get(&#x27;/&#x27;)\n    .expect(200)\n    .expect(&#x27;Content-Type&#x27;, /text\\/html/);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;59f19980-899b-43df-acfe-4cdcc005d2fc&quot;,&quot;parentUUID&quot;:&quot;d0accec7-f69f-4b60-bf7f-cbb153342fa9&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;exposes self-hosted explorer&quot;,&quot;fullTitle&quot;:&quot;HomePage exposes self-hosted explorer&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:3,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await client\n    .get(&#x27;/explorer/&#x27;)\n    .expect(200)\n    .expect(&#x27;Content-Type&#x27;, /text\\/html/)\n    .expect(/&lt;title&gt;LoopBack API Explorer/);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;422b0275-17f6-4fdf-b8b1-16ed0a7a9491&quot;,&quot;parentUUID&quot;:&quot;d0accec7-f69f-4b60-bf7f-cbb153342fa9&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;59f19980-899b-43df-acfe-4cdcc005d2fc&quot;,&quot;422b0275-17f6-4fdf-b8b1-16ed0a7a9491&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:17,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;76ef0303-0ee1-43a3-91bd-cd0088c16e56&quot;,&quot;title&quot;:&quot;PingController&quot;,&quot;fullFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/dist/__tests__/acceptance/ping.controller.acceptance.js&quot;,&quot;file&quot;:&quot;/dist/__tests__/acceptance/ping.controller.acceptance.js&quot;,&quot;beforeHooks&quot;:[{&quot;title&quot;:&quot;\&quot;before all\&quot; hook: setupApplication in \&quot;PingController\&quot;&quot;,&quot;fullTitle&quot;:&quot;PingController \&quot;before all\&quot; hook: setupApplication in \&quot;PingController\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:10,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;({ app, client } = await (0, test_helper_1.setupApplication)());&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d279c3f9-2e06-4877-9018-263ce0d98df7&quot;,&quot;parentUUID&quot;:&quot;76ef0303-0ee1-43a3-91bd-cd0088c16e56&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;afterHooks&quot;:[{&quot;title&quot;:&quot;\&quot;after all\&quot; hook in \&quot;PingController\&quot;&quot;,&quot;fullTitle&quot;:&quot;PingController \&quot;after all\&quot; hook in \&quot;PingController\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:0,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await app.stop();&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;c1d191e3-b9fd-4ff3-9552-7f2282a346bf&quot;,&quot;parentUUID&quot;:&quot;76ef0303-0ee1-43a3-91bd-cd0088c16e56&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;tests&quot;:[{&quot;title&quot;:&quot;invokes GET /ping&quot;,&quot;fullTitle&quot;:&quot;PingController invokes GET /ping&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:4,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;const res = await client.get(&#x27;/ping?msg=world&#x27;).expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: &#x27;Hello from LoopBack&#x27; });&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0c133cd5-6d83-434a-bbf9-245ce00ae4c2&quot;,&quot;parentUUID&quot;:&quot;76ef0303-0ee1-43a3-91bd-cd0088c16e56&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;0c133cd5-6d83-434a-bbf9-245ce00ae4c2&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:4,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;11.7.1&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;mochawesome&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;mochawesome&quot;,&quot;reportDir&quot;:&quot;mochawesome-report&quot;,&quot;reportTitle&quot;:&quot;user-tenant-service&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;mochawesome-report/assets&quot;,&quot;jsonFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/mochawesome-report/mochawesome.json&quot;,&quot;htmlFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/mochawesome-report/mochawesome.html&quot;}"><div id="report"></div><script src="assets/app.js"></script></body></html>