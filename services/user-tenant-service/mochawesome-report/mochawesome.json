{"stats": {"suites": 2, "tests": 3, "passes": 3, "pending": 0, "failures": 0, "start": "2025-07-04T04:26:28.091Z", "end": "2025-07-04T04:26:28.194Z", "duration": 103, "testsRegistered": 3, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "1e27e35c-a37c-4100-9db2-21fc13827a8e", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "d0accec7-f69f-4b60-bf7f-cbb153342fa9", "title": "HomePage", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/dist/__tests__/acceptance/home-page.acceptance.js", "file": "/dist/__tests__/acceptance/home-page.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"HomePage\"", "fullTitle": "HomePage \"before all\" hook: setupApplication in \"HomePage\"", "timedOut": false, "duration": 70, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "a9a4afba-fe8f-476b-a659-011ca8342870", "parentUUID": "d0accec7-f69f-4b60-bf7f-cbb153342fa9", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"HomePage\"", "fullTitle": "HomePage \"after all\" hook in \"HomePage\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "0ae0b2a7-7776-40a9-ac3d-419750acafa8", "parentUUID": "d0accec7-f69f-4b60-bf7f-cbb153342fa9", "isHook": true, "skipped": false}], "tests": [{"title": "exposes a default home page", "fullTitle": "HomePage exposes a default home page", "timedOut": false, "duration": 14, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/);", "err": {}, "uuid": "59f19980-899b-43df-acfe-4cdcc005d2fc", "parentUUID": "d0accec7-f69f-4b60-bf7f-cbb153342fa9", "isHook": false, "skipped": false}, {"title": "exposes self-hosted explorer", "fullTitle": "HomePage exposes self-hosted explorer", "timedOut": false, "duration": 3, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/explorer/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/)\n    .expect(/<title>LoopBack API Explorer/);", "err": {}, "uuid": "422b0275-17f6-4fdf-b8b1-16ed0a7a9491", "parentUUID": "d0accec7-f69f-4b60-bf7f-cbb153342fa9", "isHook": false, "skipped": false}], "suites": [], "passes": ["59f19980-899b-43df-acfe-4cdcc005d2fc", "422b0275-17f6-4fdf-b8b1-16ed0a7a9491"], "failures": [], "pending": [], "skipped": [], "duration": 17, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "76ef0303-0ee1-43a3-91bd-cd0088c16e56", "title": "PingController", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/user-tenant-service/dist/__tests__/acceptance/ping.controller.acceptance.js", "file": "/dist/__tests__/acceptance/ping.controller.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"PingController\"", "fullTitle": "PingController \"before all\" hook: setupApplication in \"PingController\"", "timedOut": false, "duration": 10, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "d279c3f9-2e06-4877-9018-263ce0d98df7", "parentUUID": "76ef0303-0ee1-43a3-91bd-cd0088c16e56", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"PingController\"", "fullTitle": "PingController \"after all\" hook in \"PingController\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "c1d191e3-b9fd-4ff3-9552-7f2282a346bf", "parentUUID": "76ef0303-0ee1-43a3-91bd-cd0088c16e56", "isHook": true, "skipped": false}], "tests": [{"title": "invokes GET /ping", "fullTitle": "PingController invokes GET /ping", "timedOut": false, "duration": 4, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const res = await client.get('/ping?msg=world').expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: 'Hello from LoopBack' });", "err": {}, "uuid": "0c133cd5-6d83-434a-bbf9-245ce00ae4c2", "parentUUID": "76ef0303-0ee1-43a3-91bd-cd0088c16e56", "isHook": false, "skipped": false}], "suites": [], "passes": ["0c133cd5-6d83-434a-bbf9-245ce00ae4c2"], "failures": [], "pending": [], "skipped": [], "duration": 4, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "11.7.1"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}