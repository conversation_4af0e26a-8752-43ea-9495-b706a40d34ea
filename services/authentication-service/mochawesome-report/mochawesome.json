{"stats": {"suites": 2, "tests": 3, "passes": 3, "pending": 0, "failures": 0, "start": "2025-07-04T04:26:23.651Z", "end": "2025-07-04T04:26:23.935Z", "duration": 284, "testsRegistered": 3, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "827a4a2f-b294-4b93-b1fe-916b3ea7273d", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "35071a37-9271-4de6-b46e-12e3f7c482e2", "title": "HomePage", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/authentication-service/dist/__tests__/acceptance/home-page.acceptance.js", "file": "/dist/__tests__/acceptance/home-page.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"HomePage\"", "fullTitle": "HomePage \"before all\" hook: setupApplication in \"HomePage\"", "timedOut": false, "duration": 197, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "3d5c2006-195c-4da4-80c5-d7795239539d", "parentUUID": "35071a37-9271-4de6-b46e-12e3f7c482e2", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"HomePage\"", "fullTitle": "HomePage \"after all\" hook in \"HomePage\"", "timedOut": false, "duration": 1, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "c17a6a08-4844-4f41-bf17-c8353b3068b7", "parentUUID": "35071a37-9271-4de6-b46e-12e3f7c482e2", "isHook": true, "skipped": false}], "tests": [{"title": "exposes a default home page", "fullTitle": "HomePage exposes a default home page", "timedOut": false, "duration": 44, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/);", "err": {}, "uuid": "d2e97e44-9007-45ee-a490-1e3a6534c4bc", "parentUUID": "35071a37-9271-4de6-b46e-12e3f7c482e2", "isHook": false, "skipped": false}, {"title": "exposes self-hosted explorer", "fullTitle": "HomePage exposes self-hosted explorer", "timedOut": false, "duration": 9, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/explorer/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/)\n    .expect(/<title>LoopBack API Explorer/);", "err": {}, "uuid": "f5674b83-b07d-4810-8135-9222cd1b12a5", "parentUUID": "35071a37-9271-4de6-b46e-12e3f7c482e2", "isHook": false, "skipped": false}], "suites": [], "passes": ["d2e97e44-9007-45ee-a490-1e3a6534c4bc", "f5674b83-b07d-4810-8135-9222cd1b12a5"], "failures": [], "pending": [], "skipped": [], "duration": 53, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "6a8c0269-2c39-47c6-8ed1-662ebf7c517a", "title": "PingController", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/authentication-service/dist/__tests__/acceptance/ping.controller.acceptance.js", "file": "/dist/__tests__/acceptance/ping.controller.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"PingController\"", "fullTitle": "PingController \"before all\" hook: setupApplication in \"PingController\"", "timedOut": false, "duration": 21, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "bbb85cd8-e1ff-4422-bc1c-c1117f25a053", "parentUUID": "6a8c0269-2c39-47c6-8ed1-662ebf7c517a", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"PingController\"", "fullTitle": "PingController \"after all\" hook in \"PingController\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "f065a975-149c-483a-bb1c-5ccf4b628ca9", "parentUUID": "6a8c0269-2c39-47c6-8ed1-662ebf7c517a", "isHook": true, "skipped": false}], "tests": [{"title": "invokes GET /ping", "fullTitle": "PingController invokes GET /ping", "timedOut": false, "duration": 8, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const res = await client.get('/ping?msg=world').expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: 'Hello from LoopBack' });", "err": {}, "uuid": "b3b8a934-8b05-4e53-946e-fda5f83ba314", "parentUUID": "6a8c0269-2c39-47c6-8ed1-662ebf7c517a", "isHook": false, "skipped": false}], "suites": [], "passes": ["b3b8a934-8b05-4e53-946e-fda5f83ba314"], "failures": [], "pending": [], "skipped": [], "duration": 8, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "11.7.1"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}