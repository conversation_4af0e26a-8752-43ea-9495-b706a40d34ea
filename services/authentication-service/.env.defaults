NODE_ENV=production
LOG_LEVEL=info
USER_TEMP_PASSWORD=temp123!@
GOOGLE_AUTH_URL=q
GOOGLE_AUTH_CLIENT_ID=a
GOOGLE_AUTH_CLIENT_SECRET=q
GOOGLE_AUTH_TOKEN_URL=q
GOOGLE_AUTH_CALLBACK_URL=q
GOOGLE_TOKEN_INFO_URL=q
INSTAGRAM_AUTH_URL=q
INSTAGRAM_AUTH_CLIENT_ID=a
INSTAGRAM_AUTH_CLIENT_SECRET=q
INSTAGRAM_AUTH_TOKEN_URL=q
INSTAGRAM_AUTH_CALLBACK_URL=q
SAML_URL=q
SAML_CLIENT_ID=a
SAML_CLIENT_SECRET=q
SAML_TOKEN_URL=q
SAML_CALLBACK_URL=q
APPLE_AUTH_URL=q
APPLE_AUTH_CLIENT_ID=a
APPLE_AUTH_TEAM_ID=q
APPLE_AUTH_KEY_ID=q
APPLE_AUTH_CALLBACK_URL=q
FACEBOOK_AUTH_URL=q
FACEBOOK_AUTH_CLIENT_ID=a
FACEBOOK_AUTH_CLIENT_SECRET=q
FACEBOOK_AUTH_TOKEN_URL=q
FACEBOOK_AUTH_CALLBACK_URL=q
REDIS_PORT=a
REDIS_HOST=a
REDIS_URL=
REDIS_PASSWORD=a
REDIS_DATABASE=a
FORGOT_PASSWORD_LINK_EXPIRY=30
REQUEST_SIGNUP_LINK_EXPIRY=30

# AZURE AD
#boolean values will be 0 or 1

AZURE_AUTH_ENABLED=0
AZURE_IDENTITY_METADATA=https://login.microsoftonline.com/common/.well-known/openid-configuration
AZURE_AUTH_CLIENT_ID=a
AZURE_AUTH_REDIRECT_URL=url
AZURE_AUTH_CLIENT_SECRET=client_secret
AZURE_AUTH_ALLOW_HTTP_REDIRECT=1
AZURE_AUTH_COOKIE_INSTEAD_SESSION=1
AZURE_AUTH_PASS_REQ_CALLBACK=0
AZURE_AUTH_VALIDATE_ISSUER=0
AZURE_AUTH_B2C_TENANT=0
AZURE_AUTH_CLOCK_SKEW=300
AZURE_AUTH_LOG_LEVEL=
AZURE_AUTH_LOG_PII=1
AZURE_AUTH_NONCE_TIME=3600
AZURE_AUTH_NONCE_COUNT=10
AZURE_AUTH_ISSUER=

# key is 32 bit

AZURE_AUTH_COOKIE_KEY=

#iv is 12 bit

AZURE_AUTH_COOKIE_IV=

MAX_JWT_KEYS=2
