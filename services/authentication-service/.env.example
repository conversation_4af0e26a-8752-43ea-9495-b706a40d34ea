NODE_ENV=
LOG_LEVEL=
DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_DATABASE=
DB_SCHEMA=
REDIS_HOST=
REDIS_PORT=
REDIS_URL=
REDIS_PASSWORD=
REDIS_DATABASE=
PRIVATE_DECRYPTION_KEY=
JWT_SECRET=
JWT_ISSUER=
USER_TEMP_PASSWORD=
GOOGLE_AUTH_URL=
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_CLIENT_SECRET=
GOOGLE_AUTH_TOKEN_URL=
GOOGLE_AUTH_CALLBACK_URL=
GOOGLE_TOKEN_INFO_URL=
SAML_URL=
SAML_CLIENT_ID=
SAML_CLIENT_SECRET=
SAML_TOKEN_URL=
SAML_CALLBACK_URL=
INSTAGRAM_AUTH_URL=
INSTAGRAM_AUTH_CLIENT_ID=
INSTAGRAM_AUTH_CLIENT_SECRET=
INSTAGRAM_AUTH_TOKEN_URL=
INSTAGRAM_AUTH_CALLBACK_URL=
APPLE_AUTH_URL=
APPLE_AUTH_CLIENT_ID=
APPLE_AUTH_TEAM_ID=
APPLE_AUTH_KEY_ID=
APPLE_AUTH_CALLBACK_URL=
FACEBOOK_AUTH_URL=
FACEBOOK_AUTH_CLIENT_ID=
FACEBOOK_AUTH_CLIENT_SECRET=
FACEBOOK_AUTH_TOKEN_URL=
FACEBOOK_AUTH_CALLBACK_URL=
FORGOT_PASSWORD_LINK_EXPIRY=
KEYCLOAK_HOST=
KEYCLOAK_REALM=
KEYCLOAK_CLIENT_ID=
KEYCLOAK_CLIENT_SECRET=
KEYCLOAK_CALLBACK_URL=

# AZURE AD
#boolean values will be 0 or 1

AZURE_AUTH_ENABLED=
AZURE_IDENTITY_METADATA=
AZURE_AUTH_CLIENT_ID=
AZURE_AUTH_REDIRECT_URL=
AZURE_AUTH_CLIENT_SECRET=
AZURE_AUTH_ALLOW_HTTP_REDIRECT=
AZURE_AUTH_COOKIE_INSTEAD_SESSION=
AZURE_AUTH_PASS_REQ_CALLBACK=
AZURE_AUTH_VALIDATE_ISSUER=
AZURE_AUTH_B2C_TENANT=
AZURE_AUTH_CLOCK_SKEW=
AZURE_AUTH_LOG_LEVEL=
AZURE_AUTH_LOG_PII=
AZURE_AUTH_NONCE_TIME=
AZURE_AUTH_NONCE_COUNT=
AZURE_AUTH_ISSUER=

# key is 32 bit

AZURE_AUTH_COOKIE_KEY=

#iv is 12 bit

AZURE_AUTH_COOKIE_IV=


AUTH0_DOMAIN=
AUTH0_CLIENT_ID=
AUTH0_CLIENT_SECRET=
AUTH0_CALLBACK_URL=

MAX_JWT_KEYS=
JWT_PRIVATE_KEY_PASSPHRASE=
API_BASE_URL=
