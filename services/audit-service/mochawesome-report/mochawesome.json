{"stats": {"suites": 2, "tests": 3, "passes": 3, "pending": 0, "failures": 0, "start": "2025-07-04T04:26:23.888Z", "end": "2025-07-04T04:26:24.160Z", "duration": 272, "testsRegistered": 3, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "da4666d0-2c8c-434c-a236-76a43dc8a8cd", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "a5fdfaaa-2cd6-418a-aa8e-9ef68acc413c", "title": "HomePage", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/audit-service/dist/__tests__/acceptance/home-page.acceptance.js", "file": "/dist/__tests__/acceptance/home-page.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"HomePage\"", "fullTitle": "HomePage \"before all\" hook: setupApplication in \"HomePage\"", "timedOut": false, "duration": 151, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "7262eabb-d76f-483c-9525-cd78491d9b80", "parentUUID": "a5fdfaaa-2cd6-418a-aa8e-9ef68acc413c", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"HomePage\"", "fullTitle": "HomePage \"after all\" hook in \"HomePage\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "952dbe40-1656-4a80-82f8-b37c301b93f9", "parentUUID": "a5fdfaaa-2cd6-418a-aa8e-9ef68acc413c", "isHook": true, "skipped": false}], "tests": [{"title": "exposes a default home page", "fullTitle": "HomePage exposes a default home page", "timedOut": false, "duration": 81, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/);", "err": {}, "uuid": "1fa0c4ae-def8-4ce2-98b8-538168d6eb88", "parentUUID": "a5fdfaaa-2cd6-418a-aa8e-9ef68acc413c", "isHook": false, "skipped": false}, {"title": "exposes self-hosted explorer", "fullTitle": "HomePage exposes self-hosted explorer", "timedOut": false, "duration": 9, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/explorer/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/)\n    .expect(/<title>LoopBack API Explorer/);", "err": {}, "uuid": "da18a134-7695-443f-be91-b107a8d1175e", "parentUUID": "a5fdfaaa-2cd6-418a-aa8e-9ef68acc413c", "isHook": false, "skipped": false}], "suites": [], "passes": ["1fa0c4ae-def8-4ce2-98b8-538168d6eb88", "da18a134-7695-443f-be91-b107a8d1175e"], "failures": [], "pending": [], "skipped": [], "duration": 90, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "6c2541c7-7184-4245-a945-49bd29506eb0", "title": "PingController", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/audit-service/dist/__tests__/acceptance/ping.controller.acceptance.js", "file": "/dist/__tests__/acceptance/ping.controller.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"PingController\"", "fullTitle": "PingController \"before all\" hook: setupApplication in \"PingController\"", "timedOut": false, "duration": 11, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "0d5a90c3-4426-42c5-968e-5e16fe9a8226", "parentUUID": "6c2541c7-7184-4245-a945-49bd29506eb0", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"PingController\"", "fullTitle": "PingController \"after all\" hook in \"PingController\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "ea774d3f-4299-4907-919c-26638cec8fdd", "parentUUID": "6c2541c7-7184-4245-a945-49bd29506eb0", "isHook": true, "skipped": false}], "tests": [{"title": "invokes GET /ping", "fullTitle": "PingController invokes GET /ping", "timedOut": false, "duration": 4, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const res = await client.get('/ping?msg=world').expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: 'Hello from LoopBack' });", "err": {}, "uuid": "06a1505a-eca7-4ebe-86a7-612d60b151bf", "parentUUID": "6c2541c7-7184-4245-a945-49bd29506eb0", "isHook": false, "skipped": false}], "suites": [], "passes": ["06a1505a-eca7-4ebe-86a7-612d60b151bf"], "failures": [], "pending": [], "skipped": [], "duration": 4, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "11.7.1"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}