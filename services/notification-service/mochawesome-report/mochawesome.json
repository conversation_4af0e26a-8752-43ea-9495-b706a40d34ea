{"stats": {"suites": 2, "tests": 3, "passes": 3, "pending": 0, "failures": 0, "start": "2025-07-04T04:26:23.611Z", "end": "2025-07-04T04:26:23.930Z", "duration": 319, "testsRegistered": 3, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "135d000a-20e1-4ba8-a71d-b63712589ee8", "title": "", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "20f11e50-e5de-44c5-83c3-83487afd870b", "title": "HomePage", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/dist/__tests__/acceptance/home-page.acceptance.js", "file": "/dist/__tests__/acceptance/home-page.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"HomePage\"", "fullTitle": "HomePage \"before all\" hook: setupApplication in \"HomePage\"", "timedOut": false, "duration": 226, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "b1907b54-2873-4f53-9bdf-31b6bd52cfff", "parentUUID": "20f11e50-e5de-44c5-83c3-83487afd870b", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"HomePage\"", "fullTitle": "HomePage \"after all\" hook in \"HomePage\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "0e63781e-d932-4708-b4c4-595aa7281281", "parentUUID": "20f11e50-e5de-44c5-83c3-83487afd870b", "isHook": true, "skipped": false}], "tests": [{"title": "exposes a default home page", "fullTitle": "HomePage exposes a default home page", "timedOut": false, "duration": 32, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/);", "err": {}, "uuid": "968d3671-af75-4df7-8f67-84c418c7fb2f", "parentUUID": "20f11e50-e5de-44c5-83c3-83487afd870b", "isHook": false, "skipped": false}, {"title": "exposes self-hosted explorer", "fullTitle": "HomePage exposes self-hosted explorer", "timedOut": false, "duration": 8, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "await client\n    .get('/explorer/')\n    .expect(200)\n    .expect('Content-Type', /text\\/html/)\n    .expect(/<title>LoopBack API Explorer/);", "err": {}, "uuid": "7bbf6d72-c94d-4ef5-9462-cb03952fa5bc", "parentUUID": "20f11e50-e5de-44c5-83c3-83487afd870b", "isHook": false, "skipped": false}], "suites": [], "passes": ["968d3671-af75-4df7-8f67-84c418c7fb2f", "7bbf6d72-c94d-4ef5-9462-cb03952fa5bc"], "failures": [], "pending": [], "skipped": [], "duration": 40, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "648654a5-e3a9-45e4-93ff-12d51b090ab9", "title": "PingController", "fullFile": "/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/dist/__tests__/acceptance/ping.controller.acceptance.js", "file": "/dist/__tests__/acceptance/ping.controller.acceptance.js", "beforeHooks": [{"title": "\"before all\" hook: setupApplication in \"PingController\"", "fullTitle": "PingController \"before all\" hook: setupApplication in \"PingController\"", "timedOut": false, "duration": 19, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "({ app, client } = await (0, test_helper_1.setupApplication)());", "err": {}, "uuid": "05943523-fa3c-48f7-af0f-1a73f57ca71e", "parentUUID": "648654a5-e3a9-45e4-93ff-12d51b090ab9", "isHook": true, "skipped": false}], "afterHooks": [{"title": "\"after all\" hook in \"PingController\"", "fullTitle": "PingController \"after all\" hook in \"PingController\"", "timedOut": false, "duration": 0, "state": null, "speed": null, "pass": false, "fail": false, "pending": false, "context": null, "code": "await app.stop();", "err": {}, "uuid": "e7d20960-946b-423a-a78d-aabec67ba9a5", "parentUUID": "648654a5-e3a9-45e4-93ff-12d51b090ab9", "isHook": true, "skipped": false}], "tests": [{"title": "invokes GET /ping", "fullTitle": "PingController invokes GET /ping", "timedOut": false, "duration": 5, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "const res = await client.get('/ping?msg=world').expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: 'Hello from LoopBack' });", "err": {}, "uuid": "6ce7611e-2523-4985-9ebe-0883323b7f96", "parentUUID": "648654a5-e3a9-45e4-93ff-12d51b090ab9", "isHook": false, "skipped": false}], "suites": [], "passes": ["6ce7611e-2523-4985-9ebe-0883323b7f96"], "failures": [], "pending": [], "skipped": [], "duration": 5, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "11.7.1"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "mochawesome", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": null, "version": "6.2.0"}}}