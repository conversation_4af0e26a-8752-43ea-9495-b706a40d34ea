<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets/app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:2,&quot;tests&quot;:3,&quot;passes&quot;:3,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-07-04T04:26:23.611Z&quot;,&quot;end&quot;:&quot;2025-07-04T04:26:23.930Z&quot;,&quot;duration&quot;:319,&quot;testsRegistered&quot;:3,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;135d000a-20e1-4ba8-a71d-b63712589ee8&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;20f11e50-e5de-44c5-83c3-83487afd870b&quot;,&quot;title&quot;:&quot;HomePage&quot;,&quot;fullFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/dist/__tests__/acceptance/home-page.acceptance.js&quot;,&quot;file&quot;:&quot;/dist/__tests__/acceptance/home-page.acceptance.js&quot;,&quot;beforeHooks&quot;:[{&quot;title&quot;:&quot;\&quot;before all\&quot; hook: setupApplication in \&quot;HomePage\&quot;&quot;,&quot;fullTitle&quot;:&quot;HomePage \&quot;before all\&quot; hook: setupApplication in \&quot;HomePage\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:226,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;({ app, client } = await (0, test_helper_1.setupApplication)());&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;b1907b54-2873-4f53-9bdf-31b6bd52cfff&quot;,&quot;parentUUID&quot;:&quot;20f11e50-e5de-44c5-83c3-83487afd870b&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;afterHooks&quot;:[{&quot;title&quot;:&quot;\&quot;after all\&quot; hook in \&quot;HomePage\&quot;&quot;,&quot;fullTitle&quot;:&quot;HomePage \&quot;after all\&quot; hook in \&quot;HomePage\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:0,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await app.stop();&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0e63781e-d932-4708-b4c4-595aa7281281&quot;,&quot;parentUUID&quot;:&quot;20f11e50-e5de-44c5-83c3-83487afd870b&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;tests&quot;:[{&quot;title&quot;:&quot;exposes a default home page&quot;,&quot;fullTitle&quot;:&quot;HomePage exposes a default home page&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:32,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await client\n    .get(&#x27;/&#x27;)\n    .expect(200)\n    .expect(&#x27;Content-Type&#x27;, /text\\/html/);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;968d3671-af75-4df7-8f67-84c418c7fb2f&quot;,&quot;parentUUID&quot;:&quot;20f11e50-e5de-44c5-83c3-83487afd870b&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;exposes self-hosted explorer&quot;,&quot;fullTitle&quot;:&quot;HomePage exposes self-hosted explorer&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:8,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await client\n    .get(&#x27;/explorer/&#x27;)\n    .expect(200)\n    .expect(&#x27;Content-Type&#x27;, /text\\/html/)\n    .expect(/&lt;title&gt;LoopBack API Explorer/);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;7bbf6d72-c94d-4ef5-9462-cb03952fa5bc&quot;,&quot;parentUUID&quot;:&quot;20f11e50-e5de-44c5-83c3-83487afd870b&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;968d3671-af75-4df7-8f67-84c418c7fb2f&quot;,&quot;7bbf6d72-c94d-4ef5-9462-cb03952fa5bc&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:40,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;648654a5-e3a9-45e4-93ff-12d51b090ab9&quot;,&quot;title&quot;:&quot;PingController&quot;,&quot;fullFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/dist/__tests__/acceptance/ping.controller.acceptance.js&quot;,&quot;file&quot;:&quot;/dist/__tests__/acceptance/ping.controller.acceptance.js&quot;,&quot;beforeHooks&quot;:[{&quot;title&quot;:&quot;\&quot;before all\&quot; hook: setupApplication in \&quot;PingController\&quot;&quot;,&quot;fullTitle&quot;:&quot;PingController \&quot;before all\&quot; hook: setupApplication in \&quot;PingController\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:19,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;({ app, client } = await (0, test_helper_1.setupApplication)());&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;05943523-fa3c-48f7-af0f-1a73f57ca71e&quot;,&quot;parentUUID&quot;:&quot;648654a5-e3a9-45e4-93ff-12d51b090ab9&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;afterHooks&quot;:[{&quot;title&quot;:&quot;\&quot;after all\&quot; hook in \&quot;PingController\&quot;&quot;,&quot;fullTitle&quot;:&quot;PingController \&quot;after all\&quot; hook in \&quot;PingController\&quot;&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:0,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;await app.stop();&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;e7d20960-946b-423a-a78d-aabec67ba9a5&quot;,&quot;parentUUID&quot;:&quot;648654a5-e3a9-45e4-93ff-12d51b090ab9&quot;,&quot;isHook&quot;:true,&quot;skipped&quot;:false}],&quot;tests&quot;:[{&quot;title&quot;:&quot;invokes GET /ping&quot;,&quot;fullTitle&quot;:&quot;PingController invokes GET /ping&quot;,&quot;timedOut&quot;:false,&quot;duration&quot;:5,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;const res = await client.get(&#x27;/ping?msg=world&#x27;).expect(200);\n(0, testlab_1.expect)(res.body).to.containEql({ greeting: &#x27;Hello from LoopBack&#x27; });&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6ce7611e-2523-4985-9ebe-0883323b7f96&quot;,&quot;parentUUID&quot;:&quot;648654a5-e3a9-45e4-93ff-12d51b090ab9&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;6ce7611e-2523-4985-9ebe-0883323b7f96&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:5,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;11.7.1&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;mochawesome&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;mochawesome&quot;,&quot;reportDir&quot;:&quot;mochawesome-report&quot;,&quot;reportTitle&quot;:&quot;notification-service&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;mochawesome-report/assets&quot;,&quot;jsonFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/mochawesome-report/mochawesome.json&quot;,&quot;htmlFile&quot;:&quot;/Users/<USER>/Desktop/distek/distek-saas-control-plane-api/services/notification-service/mochawesome-report/mochawesome.html&quot;}"><div id="report"></div><script src="assets/app.js"></script></body></html>