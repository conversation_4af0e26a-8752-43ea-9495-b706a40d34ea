#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Section for git-secrets
if ! command -v git-secrets &> /dev/null 2>&1 
then
    echo "git-secrets is not installed. Please run 'brew install git-secrets' or visit https://github.com/awslabs/git-secrets#installing-git-secrets"
    exit 1
fi

# Initialise git-secrets configuration
git-secrets --register-aws > /dev/null

echo "Running git-secrets..."
# Scans all files that are about to be committed.
git-secrets --pre_commit_hook -- "$@"

npm test