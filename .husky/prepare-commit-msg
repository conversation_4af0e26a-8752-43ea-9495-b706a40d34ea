#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

exec < /dev/tty && npx cz --hook || true

# Section for git-secrets
if ! command -v git-secrets &> /dev/null 2>&1 
then
    echo "git-secrets is not installed. Please run 'brew install git-secrets' or visit https://github.com/awslabs/git-secrets#installing-git-secrets"
    exit 1
fi

# Initialise git-secrets configuration
git-secrets --register-aws > /dev/null

echo "Running git-secrets..."
# Determines if merging in a commit will introduce tainted history.
git-secrets --prepare_commit_msg_hook -- "$@"